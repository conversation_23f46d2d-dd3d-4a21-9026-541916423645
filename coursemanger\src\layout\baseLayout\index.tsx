import { reqLogs, courseOfSensitivew } from '@/api/addCourse';
import baseInfo from '@/api/baseInfo';
import { CheckService } from '@/api/check';
import { authorityIdentification, getUserJurisdictionV2 } from '@/api/course';
import { queryNoCheckHomework } from '@/api/homework';
import {
  fetchSemeter,
  getCourseFloorDetail,
  getCourse_floor,
  offShelfCourse,
  offShelfCourseNew,
  publishMapNew,
  releaseCourse,
  releaseCourseNew,
  updateRelated,
} from '@/api/mooclist';
import NoticeService from '@/api/notice';
import QAService from '@/api/qa';
import { ReactComponent as homework_icon } from '@/assets/imgs/icon/homework.svg';
import Header from '@/components/Header';
import LoggerModal from '@/components/LoggerModal';
import NPUHeader from '@/components/NPUHeader';
import { IconFont } from '@/components/iconFont';
import Loading from '@/components/loading/loading';
import useSemester from '@/hooks/semester';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { IGlobalModelState } from '@/models/global';
import { CUSTOMER_NPU, typeEn } from '@/permission/moduleCfg';
import { changeURLArg } from '@/utils';
import Icon, {
  LeftCircleFilled,
  LeftOutlined,
  QuestionCircleOutlined,
  UnorderedListOutlined,
  FormOutlined,
  FileDoneOutlined,
  ExclamationCircleTwoTone
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Dropdown,
  Form,
  Input,
  Spin,
  Menu,
  Modal,
  Radio,
  Select,
  Space,
  Switch,
  Tooltip,
  message,
} from 'antd';
import moment from 'moment';
import React, {
  FC,
  ReactNode,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useHistory, useSelector } from 'umi';
import './index.less';
import { useRefresh } from "@/hooks/useRefresh";



interface IauthorityList {
  icon: any;
  name: ReactNode;
  path: string;
  key: string;
  divider?: boolean;
  show: boolean;
  type?: string;
}

const HomeworkIcon = (props: any) => (
  <Icon component={homework_icon} {...props} />
);

const EditCourse: FC<{}> = ({ children }) => {
  const { handleOperate } = useRefresh()
  let history: any = useHistory();
  const [activeMenuKey, setActiveMenuKey] = useState(
    [...history.location.pathname.split('/')].pop(),
  );

  const dispatch = useDispatch();

  const [form] = Form.useForm();
  const [relatedForm] = Form.useForm();
  const [courseData, setCourseData] = useState<any>();
  const [basicCollege, setBasicCollege] = useState<string>('');
  const [basicMajors, setBasicMajors] = useState<string>('');
  const [basicCover, setBasicCover] = useState<string>('');
  const [publishCount, setPublishCount] = useState<string>('');
  const [resourcesCount, setResourcesCount] = useState<string>('');
  const [unReadCount, setUnReadCount] = useState<number>(0);
  // const [peopleCount, setPeoplCount] = useState<string>('');
  const [publishStatus, setPublishStatus] = useState<number>(0);
  // const [mySpocList, setMySpocList] = useState<any>([]);
  const [authorityList, setAuthorityList] = useState<IauthorityList[]>([]);

  const [release, setRelease] = useState<boolean>(false);
  const [releaseLoading, setReleaseLoading] = useState<boolean>(false);
  const [sensitiveWordInfos, setSensitiveWordInfos] = useState<any>([]); // 敏感词信息
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [recall, setRecall] = useState<boolean>(false);
  const [isReject, setIsReject] = useState<boolean>(false);
  const [relatedCourse, setRelatedCourse] = useState<any>({});
  const [rejectReason, setRejectReason] = useState<string>('');
  const queryData = history.location.query;
  const { parameterConfig, parameterConfigObj, userInfo } = useSelector<
    { global: IGlobalModelState },
    any
  >(state => state.global);
  const { layoutUpdata, layoutQAUpdata } = useSelector<any, any>(
    state => state.updata,
  );
  const { t } = useLocale();
  const { getOptAuth, getPermission } = usePermission();
  const isMoocReview = useMemo(() => {
    return getPermission(
      ['spoc', 'training', 'mooc'],
      '_course_release_review',
      true,
      queryData.type,
    );
  }, [parameterConfig]);
  const isSuper = useMemo(() => {
    if (userInfo?.roles) {
      return (
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_sys_manager') || //是否是系统管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_course_manager') || //是否是课程管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_second_manager') || //第二权限
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1')
      );
    } else {
      return false;
    }
  }, [userInfo]);

  const [showLiveBtn, setShowLiveBtn] = useState<boolean>(false);
  const [permission, setPermission] = useState<boolean>(false);
  const [liveVisible, setLiveVisible] = useState<boolean>(false);
  const [noCheckHomework, setNoCheckHomework] = useState<number>(0);
  const [relatedCoursesModalVisible, setRelatedCoursesModalVisible] = useState<
    boolean
  >(false);
  // document.title = '教学空间';

  const [showMenus, setShowMenus] = useState<any | undefined>([]);
  const [stageMenus, setStageMenus] = useState<any>([]);
  const [showMenuSetting, setShowMenuSetting] = useState<boolean>(false);
  const [relatedLoading, setRelatedLoading] = useState<boolean>(false);

  const [param, setParam] = useState<any>({
    page: 1,
    size: 50,
    course_name: '',
    course_form: t('班级课'),
  });
  const [courseTotal, setCourseTotal] = useState<any>(0);
  const [offlineCourse, setOfflineCourse] = useState<any[]>([]);

  //课程达成度权限校验
  const verifyPermission = (type: string) => {
    // ['map', 'mooc 公开课', 'spoc 班级课' training 培训课, map 图谱课]
    if (type === 'spoc') {
      return parameterConfig?.spoc_course_achievement_display === 'true';
    }
    if (type === 'mooc') {
      return parameterConfig?.mooc_course_achievement_display === 'true';
    }
    if (type === 'training') {
      return parameterConfig?.training_course_achievement_display === 'true';
    }
    if (type === 'map') {
      return parameterConfig?.map_course_achievement_display === 'true';
    }
    return true;
  };
  const spocList: IauthorityList[] = [
    {
      icon: <IconFont type="iconkechengxinxi" />,
      name: t('课程设置'),
      path: 'baseInfo',
      key: 'baseInfo',
      show: true,
    },
    {
      icon: <IconFont type="iconzhangjieneirong" />,
      name: t('章节内容'),
      path: 'chapter',
      key: 'chapter',
      show: queryData.type !== 'map',
    },
    {
      icon: <IconFont type="iconketanghuikan" />,
      name: t('在线课堂'),
      path: 'coursereview',
      key: 'coursereview',
      show:
        queryData.type === 'spoc' && parameterConfig.spoc_onlineclass_display == 'true' &&
        parameterConfig.target_customer !== CUSTOMER_NPU,
    },
    {
      icon: <IconFont type="iconjiaocaijiaofu" />,
      name: t('教材教参'),
      path: 'teachingAssistant',
      key: 'teachingAssistant',
      divider: !getPermission(['training', 'spoc'], 'knowledge_map_display'),
      show: getPermission(
        ['training', 'spoc'],
        [
          'training_textbook_reference_display',
          'spoc_textbook_reference_display',
        ],
      ),
    },
    {
      icon: <IconFont type="icona-ditulei_ditu1" />,
      name: t('课程地图'),
      path: 'spocmap',
      key: 'spocmap',
      show: getPermission(['training', 'spoc'], 'knowledge_map_display'),
    },

    {
      icon: <IconFont type="iconshijuanguanli1" />,
      name: t('课程实践'),
      path: 'spocpractice',
      key: 'spocpractice',
      show: getPermission(['training', 'spoc'], 'course_practice'),
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('课程资料'),
      path: 'spocdata',
      key: 'spocdata',
      divider: true,
      show: getPermission(['training', 'spoc'], 'course_resource_area'),
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI教案'),
      path: 'aiJiaoAn',
      key: 'aiJiaoAn',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI出题'),
      path: 'aiChuTi',
      key: 'aiChuTi',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="icongonggao" />,
      name: t('公告'),
      path: 'coursenotice',
      key: 'coursenotice',
      show: true,
    },
    {
      icon: <HomeworkIcon />,
      name: (
        <Space size={60}>
          <span>{t('作业')}</span>
          <Badge count={noCheckHomework} overflowCount={99} />
        </Space>
      ),
      path: 'homework',
      key: 'homework',
      show: getPermission(
        ['training', 'spoc'],
        [
          'training_school_assignment_display',
          'spoc_school_assignment_display',
        ],
      ),
    },
    {
      icon: <IconFont type="icona-bianzu11" />,
      name: (
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>
            {parameterConfig.target_customer === CUSTOMER_NPU
              ? t('问学')
              : t('问答')}
          </span>
          <Badge count={unReadCount} overflowCount={99} />
        </div>
      ),

      path: 'courseqa',
      key: 'courseqa',
      divider: parameterConfig.INTERACTIVE != 'true',
      show: getPermission(['training', 'spoc'], 'question_and_answer_display'),
    },
    {
      icon: <FormOutlined />,
      name: t('问卷'),
      path: 'questionnaireManage',
      key: 'questionnaireManage',
      show: getPermission(['spoc'], 'my_quesstionnaire_display'),
    },
    {
      icon: <IconFont type="iconhudong" />,
      name: t('互动'),
      path: 'interactive',
      key: 'interactive',
      divider: true,
      show: parameterConfig.INTERACTIVE == 'true' && queryData.type === 'spoc',
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('学生管理'),
      path: 'studentmanagement',
      key: 'studentmanagement',
      show: true,
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('成绩与分组管理'),
      path: 'grade',
      key: 'grade',
      show: queryData.type === 'microMajor',
    },
    {
      // icon: <IconFont type="iconxueshengguanli" />,
      icon: <FileDoneOutlined />,
      name: t('成绩管理'),
      path: 'grademanagement',
      key: 'grademanagement',
      show: queryData.type === 'spoc' && verifyPermission(queryData.type),
    },
    {
      icon: <IconFont type="icontongji" />,
      name: t('学情统计'),
      path: 'statistics',
      key: 'statistics',
      type: 'reset',
      show: true,
    },
    {
      icon: <IconFont type="iconicon-running" />,
      name: t('课程运行情况'),
      path: 'operation',
      key: 'operation',
      show: true,
    },
    {
      icon: <IconFont type="iconicon-running" />,
      name: t('课程目标达成度'),
      path: 'completion',
      key: 'completion',
      divider: true,
      show:
        ['map', 'mooc', 'spoc', 'training'].includes(queryData.type) &&
        // parameterConfig.course_target_achievement_display === 'true',
        verifyPermission(queryData.type),
    },
  ];
  const moocList = [
    {
      icon: <IconFont type="iconkechengxinxi" />,
      name: t('课程设置'),
      path: 'moocbaseinfo',
      key: 'moocbaseinfo',
      show: true,
    },
    {
      icon: <IconFont type="iconzhangjieneirong" />,
      name: t('章节内容'),
      path: 'moocChapter',
      key: 'moocChapter',
      show: queryData.type !== 'map',
    },
    {
      icon: <IconFont type="icona-ditulei_ditu1" />,
      name: t('课程地图'),
      path: 'moocmap',
      key: 'moocmap',
      show: getPermission(['map', 'mooc'], 'knowledge_map_display'),
    },
    {
      icon: <IconFont type="iconshijuanguanli1" />,
      name: t('课程实践'),
      path: 'moocpractice',
      key: 'moocpractice',
      show: getPermission(['map', 'mooc'], 'course_practice'),
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('课程资料'),
      path: 'moocdata',
      key: 'moocdata',
      divider: true,
      show: getPermission(['map', 'mooc'], 'course_resource_area'),
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI教案'),
      path: 'aiJiaoAn',
      key: 'aiJiaoAn',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI出题'),
      path: 'aiChuTi',
      key: 'aiChuTi',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="icongonggao" />,
      name: t('公告'),
      path: 'mooccoursenotice',
      key: 'mooccoursenotice',
      show: true,
    },
    {
      icon: <HomeworkIcon />,
      name: (
        <Space size={60}>
          <span>{t('作业')}</span>
          <Badge count={noCheckHomework} overflowCount={99} />
        </Space>
      ),
      path: 'moochomework',
      key: 'moochomework',
      show: getPermission(
        ['map', 'mooc'],
        ['map_school_assignment_display', 'mooc_school_assignment_display'],
      ),
    },
    {
      icon: <IconFont type="icona-bianzu11" />,
      name: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>
            {parameterConfig.target_customer === CUSTOMER_NPU
              ? t('问学')
              : t('问答')}
          </span>
          <Badge count={unReadCount} overflowCount={99} />
        </div>
      ),

      path: 'mooccourseqa',
      key: 'mooccourseqa',
      divider: true,
      show: getPermission(['map', 'mooc'], 'question_and_answer_display'),
    },
    {
      icon: <FormOutlined />,
      name: t('问卷'),
      path: 'questionnaireManage',
      key: 'questionnaireManage',
      show: getPermission(['map', 'mooc', 'spoc'], 'my_quesstionnaire_display'),
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('学生列表'),
      path: 'studentlist',
      key: 'studentlist',
      show:
        !courseData?.entityData?.publishType ||
        courseData?.entityData?.publishType === 1,
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('学生管理'),
      path: 'studentmanagement',
      key: 'studentmanagement',
      show: courseData?.entityData?.publishType === 2,
    },
    {
      icon: <FileDoneOutlined />,
      name: t('成绩管理'),
      path: 'grademanagement',
      key: 'grademanagement',
      show: verifyPermission(queryData.type),
    },
    {
      icon: <IconFont type="icontongji" />,
      name: t('学情统计'),
      path: 'moocstatistics',
      key: 'moocstatistics',
      type: 'reset',
      show: queryData.type !== 'map',
    },
    {
      icon: <IconFont type="icontongji" />,
      name: t('学情统计'),
      path: 'mapstatistics',
      key: 'mapstatistics',
      type: 'reset',
      show: queryData.type === 'map',
    },
    {
      icon: <IconFont type="iconicon-running" />,
      name: t('课程运行情况'),
      path: 'moocoperation',
      key: 'moocoperation',
      show: queryData.type !== 'map',
    },
    {
      icon: <IconFont type="iconicon-running" />,
      name: t('课程目标达成度'),
      path: 'mooccompletion',
      key: 'mooccompletion',
      divider: true,
      // show: queryData.type === 'map',
      show:
        ['map', 'mooc', 'spoc', 'training'].includes(queryData.type) &&
        // parameterConfig.course_target_achievement_display === 'true',
        verifyPermission(queryData.type),
    },
  ];
  const microMajorList: IauthorityList[] = [
    {
      icon: <IconFont type="iconkechengxinxi" />,
      name: t('微专业设置'),
      path: 'baseInfo',
      key: 'baseInfo',
      show: true,
    },
    {
      icon: <IconFont type="iconketanghuikan" />,
      name: t('培养方案'),
      path: 'cultureprogram',
      key: 'cultureprogram',
      show: true,
    },
    {
      icon: <IconFont type="icona-ditulei_ditu1" />,
      name: t('微专业图谱'),
      path: 'spocmap',
      key: 'spocmap',
      show: true,
    },
    {
      icon: <IconFont type="icongonggao" />,
      name: t('公告'),
      path: 'coursenotice',
      key: 'coursenotice',
      show: true,
    },
    {
      icon: <HomeworkIcon />,
      name: (
        <Space size={20}>
          <span>{t('作业与测试')}</span>
          <Badge count={noCheckHomework} overflowCount={99} />
        </Space>
      ),
      path: 'micromajor/homework',
      key: 'micromajor/homework',
      show: true,
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI教案'),
      path: 'aiJiaoAn',
      key: 'aiJiaoAn',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="iconkechengziliaobeifen" />,
      name: t('AI出题'),
      path: 'aiChuTi',
      key: 'aiChuTi',
      divider: true,
      show: true,
    },
    {
      icon: <IconFont type="icona-bianzu11" />,
      name: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>
            {parameterConfig.target_customer === CUSTOMER_NPU
              ? t('问学')
              : t('问答')}
          </span>
          <Badge count={unReadCount} overflowCount={99} />
        </div>
      ),

      path: 'courseqa',
      key: 'courseqa',
      divider: parameterConfig.INTERACTIVE != 'true',
      show: true,
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('学生管理'),
      path: 'studentmanagement',
      key: 'studentmanagement',
      show: true,
    },
    {
      icon: <IconFont type="iconxueshengguanli" />,
      name: t('成绩与分组管理'),
      path: 'grade',
      key: 'grade',
      show: queryData.type === 'microMajor',
    },
    {
      icon: <IconFont type="icontongji" />,
      name: t('学情统计'),
      path: 'mapstatistics',
      key: 'mapstatistics',
      type: 'reset',
      show: true,
    },
    {
      icon: <IconFont type="iconicon-running" />,
      name: t('微专业能力达成度'),
      path: 'micromajorachievement',
      key: 'micromajorachievement',
      divider: true,
      show: true,
    },
  ];

  useEffect(() => {
    if (permission) {
      getName();
      getAct();
      // getPeo();
      if (
        getPermission(
          ['training', 'spoc'],
          ['training_course_live_display', 'spoc_course_live_display'],
        )
      ) {
        getLiveStatus();
      }
    }
  }, [layoutUpdata, permission, history.location.query.sm]);
  useEffect(() => {
    if (courseData?.entityData?.menu_show_setting) {
      const temp = JSON.parse(courseData.entityData.menu_show_setting);
      setShowMenus(temp);
    } else if (courseData) {
      const temp = mList
        .filter((item: any) => item.show)
        .map((item: any) => item.key);
      setShowMenus(temp);
    }
  }, [courseData]);

  useEffect(() => {
    if (history.location.query.type === 'mooc') {
      getSemesters();
    }
  }, [layoutUpdata]);

  useEffect(() => {
    // 问答数量改变
    getNoCheckHomework();
  }, [layoutQAUpdata, history.location.query.sm]);

  useEffect(() => {
    if (userInfo.userCode) {
      getUnRead();
    }
  }, [layoutQAUpdata, history.location.query.sm]);

  // spoc 根据权限动态控制菜单显示
  // useEffect(() => {
  //   let newSpocList = [...spocList];
  //   setMySpocList(newSpocList);
  // }, [parameterConfig, unReadCount, history.location.query.sm]);
  // spoc 修改地址
  // useEffect(() => {
  //   if (!['mooc', 'map'].includes(queryData.type) && spocList.length > 0) {
  //     let activeMenu = spocList.find(item => item.key === activeMenuKey);
  //     if (activeMenu) {
  //       return;
  //     } else {
  //       debugger
  //       history.push(
  //         `/editcourse/${spocList[0]?.path}?${location.hash.split("?")[1]}`,
  //       );
  //       setActiveMenuKey(authorityList[0]?.path);
  //     }
  //   }
  // }, [authorityList]);
  useEffect(() => {
    const key = history.location.pathname.replace('/editcourse/', '');
    setActiveMenuKey(key);
    checkAuthority();
  }, [history.location]);
  useEffect(() => {
    const dom: any = document.getElementsByClassName('help_btn');
    if (dom.length > 0) {
      dom[0].style.display = permission ? 'flex' : 'none';
    }
  }, [permission]);

  const receiveMessage = (e: any) => {
    try {
      if (e.origin === window.location.origin) {
        if (typeof e.data === 'string') {
          const { action, nodeId } = JSON.parse(e.data);
          if (action === 'micromajorAdd') {
            history.push(
              `/editmicromajor/spocmap?${location.hash.split('?')[1]}`,
            );
          } else if (action === 'micromajorEdit') {
            const queryString = location.hash.split('?')[1];
            const urlSearchParams = new URLSearchParams(queryString);
            const query = Object.fromEntries(urlSearchParams.entries());
            query.nodeId = nodeId;
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(query)) {
              if (Array.isArray(value)) {
                value.forEach(item => params.append(key, item));
              } else {
                params.append(key, value);
              }
            }
            history.push(`/editmicromajor/spocmap?${params.toString()}`);
          }
        }
      }
    } catch (e) { }
  };
  const getMicroPermission = () => {
    CheckService.queryMicroPermission({ courseId: queryData.id }).then(
      (res: any) => {
        if (res.status === 200) {
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              microPermission: res.data?.data,
            },
          });
        }
      },
    );
  };

  // 敏感词信息dom
  const sensitiveMsgListDom = (list: any) => {
    return '';
    // if (!list || list.length === 0) return ''

    // return list.map((item: any) => <div style={{ padding: '0 15px' }}>
    //   <div style={{ marginTop: 10 }}><ExclamationCircleTwoTone twoToneColor="#faad14" /> 资源  <span>{item.name}</span>：</div>
    //   <div style={{ paddingLeft: 20, margin: '10px 0' }}>
    //     {
    //       item.sensitiveInfos?.map((ele: any) => <div>
    //         <span>({ele.source}) - 包含敏感词：</span>
    //         <span>{ele.sensitiveMsgs.join('、')}</span>
    //       </div>)
    //     }
    //   </div>
    // </div>)
  }

  useEffect(() => {
    window.addEventListener('message', receiveMessage, false);
    getJurisdiction();
    if (queryData.type === 'microMajor' && queryData.action !== 'add') {
      getMicroPermission();
    }
    return () => {
      window.removeEventListener('message', receiveMessage, false);
    };
  }, []);
  const getJurisdiction = () => {
    getUserJurisdictionV2()
      .then((res: any) => {
        if (
          res &&
          res.errorCode === 'success' &&
          typeof res.extendMessage.moduleFeatures === 'object'
        ) {
          const permission = res.extendMessage.moduleFeatures;
          dispatch({
            type: 'jurisdiction/updateState',
            payload: {
              jurisdictionList: Object.keys(permission)
                .map(key => permission[key])
                .flat(2),
              modules: res.extendMessage.modules || [],
            },
          });
        } else {
          message.error(t('权限信息获取失败！'));
        }
      })
      .catch(() => {
        message.error(t('权限信息获取失败！'));
      });
  };
  //访问权限判定
  const checkAuthority = () => {
    // 此处屏蔽通过 [ 新增 ] 课程时的权限校验，后端处理数据时存在异步问题，跳转过来极有可能查询不到该课程出现跳转无权限问题；
    if (queryData.action !== 'add') {
      authorityIdentification(queryData.id).then(res => {
        if (res?.status === 200) {
          setPermission(true);
        } else if (res?.status === 403) {
          setPermission(false);
        } else if (res?.status === 400) {
          message.warning(res.message);
          window.open(`/learn/workbench/#/course`, '_self');
        }
      });
    } else {
      setPermission(true);
    }

  };
  // 默认展示页加载
  useEffect(() => {
    if (relatedCoursesModalVisible) {
      fetchSemeter().then(ress => {
        console.log(ress);
        getCourse_floor({
          ...param,
          semester: ress.extendMessage.name,
        }).then(res => {
          if (res.error_code === 'cloud_sc.0000.0000' && res.extend_message) {
            setCourseTotal(res.extend_message.count);
            if (param.page === 1) {
              setOfflineCourse(res.extend_message.results);
            } else {
              let arr = [...offlineCourse, ...res.extend_message.results];

              setOfflineCourse(arr);
            }
          }
        });
      });
    }
  }, [relatedCoursesModalVisible, param]);

  const handleScroll = (e: any) => {
    const { target } = e;
    const total = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      param.page < Math.ceil(courseTotal / param.size) //向上取整
    ) {
      setParam({
        ...param,
        page: param.page + 1,
      });
    }
  };

  const handleSearch = (value: string) => {
    setParam({
      ...param,
      page: 1,
      course_name: value,
    });
  };

  const getCourseOption = (id: any) => {
    getCourseFloorDetail({ id }).then(res => {
      if (res && res.error_code === 'cloud_sc.0000.0000') {
        const results = res.extend_message ?? {};
        setRelatedCourse(results ?? {});
      }
    });
  };

  /**
   *
   * 根据权限设置 spoc 菜单
   */
  const getName = () => {
    baseInfo
      .getCourseDetails(queryData.id, history.location.query.sm ?? 1)
      .then(res => {
        if (res && res.message === 'OK') {
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              courseDetail: res.data,
            },
          });
          setCourseData(res.data);
          sessionStorage.setItem('micro_major_course_name', res.data?.name);
          if (res.data.entityData.related_courses) {
            getCourseOption(res.data.entityData.related_courses);
          }
          console.log('课程信息', res.data);
          setPublishStatus(res.data.entityData.publishStatus);
          setBasicCollege(
            res.data.entityData.college ? res.data.entityData.college[1] : '',
          );

          let majors: string[] = [];
          res.data.entityData.major &&
            res.data.entityData.major.forEach((item: any) => {
              majors.push(item.code);
            });
          setBasicMajors(majors.join('，'));
          setBasicCover(res.data.entityData.cover);
        }
      });
  };
  const getAct = () => {
    baseInfo
      .getactivities(queryData.id, history.location.query.sm ?? 1)
      .then(res => {
        if (res && res.message === 'OK') {
          // console.log(res)
          setPublishCount(res.data.publishCount);
          setResourcesCount(res.data.resourcesCount);
        }
      });
  };
  /**
   * @description: 消息未读数量查询
   * @param {*}
   * @return {*}
   */
  const getUnRead = () => {
    QAService.fetchUnReadCount({
      user_id_of_unread: userInfo.userCode,
      link_id: queryData.id,
    }).then(res => {
      if (res && res.error_msg === 'Success') {
        setUnReadCount(res.extend_message.unread_count);
      }
    });
  };
  const getNoCheckHomework = () => {
    queryNoCheckHomework({ courseId: queryData.id }).then((res: any) => {
      if (res && res.status === 200) {
        setNoCheckHomework(res.data);
      }
    });
  };

  const handleSetRelatedCourse = (rest = {}) => {
    setRelatedLoading(true);
    relatedForm.validateFields().then(values => {
      const course = offlineCourse.find(item => item.id === values.course);
      const params: any = {
        relatedCourses: values.course,
        courseId: queryData.id,
        courseNumber: course.course_id,
        serialNumber: course.course_no,
        classTeaching: course.class_teaching,
        ...rest,
      };
      if (courseData?.entityData?.related_courses) {
        params.operateFlag = relatedType;
      }
      if (queryData.type === 'map') {
        mapPublish('2', params);
      } else {
        updateRelated(params)
          .then((res: any) => {
            if (res.status === 200) {
              getName();
              message.success(t('关联成功'));
              setRelatedCoursesModalVisible(false);
              relatedCancel();
            } else {
              message.error(res.message);
            }
          })
          .finally(() => {
            setRelatedLoading(false);
          });
      }

    });
  };
  const mList = useMemo(() => {
    const list = ['mooc', 'map'].includes(queryData.type)
      ? moocList
      : queryData.type === 'microMajor'
        ? microMajorList
        : spocList;
    return list;
  }, [parameterConfig, courseData])
  const getMenu = () => {
    const menuList = (
      <>
        {mList
          .filter((item: any) => item.show && showMenus?.includes(item.key))
          .map((item: any, index: number) => {
            if (item.divider) {
              return (
                <>
                  <Menu.Item
                    key={item.key}
                    onClick={() => {
                      if (item.path === 'spocdata') {
                        history.push(
                          `/editcourse/${item.path}?id=${queryData.id}&type=${queryData.type
                          }&courseType=${courseData.entityData.courseType
                          }&courseName=${courseData.entityData.name}&reviewid=${courseData.courseId
                          }&sm=${queryData.sm ?? 1}`,
                        );
                      } else {
                        history.push(
                          `/editcourse/${item.path}?${location.hash.split('?')[1]
                          }`,
                        );
                      }
                    }}
                  >
                    <a style={{ display: 'flex' }}>
                      {item.icon}
                      {item.name}
                    </a>
                  </Menu.Item>
                  <Menu.Divider />
                </>
              );
            } else if (item.key == 'homework') {
              return (
                <Menu.Item
                  key={item.key}
                  onClick={() => {
                    if (location.hash.includes('microMajor')) {
                      history.push(
                        `/editcourse/micromajor/${item.path}?${location.hash.split('?')[1]
                        }`,
                      );
                    } else {
                      history.push(
                        `/editcourse/${item.path}?${location.hash.split('?')[1]
                        }`,
                      );
                    }
                  }}
                >
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <a>
                      <HomeworkIcon />
                      {location.hash.includes('microMajor')
                        ? t('作业与测试')
                        : t('作业')}
                    </a>
                    <Badge count={noCheckHomework} overflowCount={99} />
                  </div>
                </Menu.Item>
              );
            } else {
              return (
                <Menu.Item
                  key={item.key}
                  onClick={() => {
                    let hashRoute = location.hash.split('?')[1];
                    // 去掉学情统计多余的studentid字段
                    if (item?.type === 'reset') {
                      const routeArr = location.hash.split('?')[1]?.split('&');
                      const newRouteArr = routeArr.reduce(
                        (pre: string[], cur: string) => {
                          if (cur.split('=')?.[0] != 'studentId') {
                            pre.push(cur);
                          }
                          return pre;
                        },
                        [],
                      );
                      hashRoute = newRouteArr.join('&');
                    }
                    history.push(`/editcourse/${item.path}?${hashRoute}`);
                  }}
                >
                  <a>
                    {item.icon}
                    {item.name}
                  </a>
                </Menu.Item>
              );
            }
          })}
      </>
    );

    return menuList;
  };


  //页面挂载需要重置closed状态
  useEffect(() => {
    window.localStorage.setItem("isClosed", "false")
  }, []);

  const [publishLoading, setPublishLoading] = useState<boolean>(false);


  useEffect(() => {
    // 点开发布弹窗时，先进行敏感词检测
    if (release) {
      checkCourseContent();
    }
  }, [release]);

  // 发布课程敏感词检测
  const checkCourseContent = () => {
    // 仅发布操作：发布前先进行敏感词检测
    if (releaseOrNot) {
      const params = {
        coursePublishInfos: [{ courseId: queryData.id }],
        courseType: courseData.entityData.courseType,
      }
      setReleaseLoading(true);
      courseOfSensitivew(params).then((res) => {
        if (res?.statusCode === 200) {
          setSensitiveWordInfos(res.data || []);
        }
      })
        .finally(() => { setReleaseLoading(false); })
    }
  }

  useEffect(() => {
    let isClosed = window.localStorage.getItem('isClosed');
    if (isClosed === "true") {
      window.localStorage.removeItem('isClosed');
    }
  }, []);

  // 发布下架课程
  const handleReleseOK = () => {
    setPublishLoading(true);
    if (releaseOrNot) {
      if (queryData.type === 'map') {
        mapPublish(courseData.entityData.publishType);
      } else {
        if (courseData.entityData.courseType == 0) {
        } else if (
          courseData.entityData.courseType == 1 ||
          courseData.entityData.courseType == 2 ||
          courseData.entityData.courseType == 3
        ) {
          releaseCourseNew(
            [queryData.id],
            courseData.entityData.courseType,
          ).then(res => {
            if (res && res.message === 'OK') {
              const messageText =
                isMoocReview && publishStatus !== 2
                  ? `${isSuper
                    ? t('提交成功，您是管理员，已为您自动完成课程发布审核')
                    : t('课程已提交至管理员审核！')
                  }`
                  : t('课程发布成功！');
              handleOperate();
              message.success(messageText);
              setPublishStatus(isMoocReview && !isSuper ? 2 : 1);
              getName();
              getAct();
              dispatch({
                type: 'updata/changeCourse',
                payload: {},
              });
            } else {
              message.error(res.message);
            }
            setRelease(false);
          }).finally(() => {
            setPublishLoading(false);
          });
        } else {
          releaseCourse(
            [queryData.id],
            courseData.entityData.courseType,
            courseData.entityData.publishStatus === 2 ? 1 : undefined,
          ).then(res => {
            if (res && res.message === 'OK') {
              const messageText =
                isMoocReview && publishStatus !== 2
                  ? `${isSuper
                    ? t('提交成功，您是管理员，已为您自动完成课程发布审核')
                    : t('课程已提交至管理员审核！')
                  }`
                  : t('课程发布成功！');
              message.success(messageText);
              setPublishStatus(isMoocReview && !isSuper ? 2 : 1);
              handleOperate();
              getName();
              getAct();
              dispatch({
                type: 'updata/changeCourse',
                payload: {},
              });
            } else {
              message.error(res.message);
            }
            setRelease(false);
          }).finally(() => {
            setPublishLoading(false);
          });
        }
      }
    } else {
      if (isReject) {
        offShelfCourseNew([queryData.id], {
          courseType: typeEn[queryData.type],
          withdrawalFlag: !isReject ? 0 : 1,
        }).then(res => {
          if (res && res.message === 'OK') {
            const messageText = !recall
              ? isReject
                ? t('课程驳回成功！')
                : t('课程取消发布成功！')
              : t('课程撤回审核成功！');
            message.success(messageText);
            setPublishStatus(0);
            handleOperate();
            getName();
            getAct();
            dispatch({
              type: 'updata/changeCourse',
              payload: {},
            });
          }
          setRelease(false);
        }).finally(() => {
          setPublishLoading(false);
        });
      } else {
        offShelfCourseNew([queryData.id], {
          // review: isReject ? 1 : undefined,
          // reasonRejection: isReject ? rejectReason : undefined,
          courseType: typeEn[queryData.type],
          withdrawalFlag: recall ? 1 : undefined,
        }).then(res => {
          if (res && res.message === 'OK') {
            const messageText = !recall
              ? isReject
                ? t('课程驳回成功！')
                : t('课程取消发布成功！')
              : t('课程撤回审核成功！');
            message.success(messageText);
            setPublishStatus(0);
            handleOperate();
            getName();
            getAct();
            dispatch({
              type: 'updata/changeCourse',
              payload: {},
            });
          }
          setRelease(false);
        }).finally(() => {
          setPublishLoading(false);
        });
      }
    }
    window.localStorage.setItem('isClosed', 'true');
  };

  const handleConfirmClose = (values: any) => {
    NoticeService.updateLiveCourse({ ...values, courseId: queryData.id }).then(
      res => {
        if (res.status === 200) {
          form.resetFields();
          setShowLiveBtn(!showLiveBtn);
          setLiveVisible(false);
        }
      },
    );
  };
  const getLiveStatus = () => {
    NoticeService.fetchLiveCourseDetail(queryData.id).then(res => {
      if (res.status === 200) {
        const data = res.data ?? {};
        setShowLiveBtn(data.staus === 1 || data.staus === 2);
      }
    });
  };
  const handleConfirm = () => {
    if (showLiveBtn) {
      handleConfirmClose({ staus: 3 });
    } else {
      form.submit();
    }
  };
  const [relatedType, setRelatedType] = useState<0 | 1>(0);
  const [relatedConfirmVis, setRelatedConfirmVis] = useState<boolean>(false);
  const restRef = useRef<any>({});
  const handleRelatedConfirm = async () => {
    // 校验课程大纲
    try {
      const { id, sm } = queryData;
      const course = relatedForm.getFieldValue('course');
      if (!course) {
        throw new Error('请选择关联课程');
      }
      if (queryData.type === 'map') {
        handleSetRelatedCourse();
        return;
      }
      const course_code = offlineCourse?.find(item => item.id === course)
        ?.course_id;
      const {
        data: { data, message: msg },
      } = await CheckService.syllabusLast({
        courseId: id,
        courseSemester: sm,
        course_code,
      });
      if (!data) {
        throw new Error(msg);
      }
      const { popFrame, ...rest } = data;

      if (popFrame) {
        Modal.confirm({
          content: t(
            '检测到课程教学大纲与绑定的课程不一致，是否按当前绑定的课程更换教学大纲？',
          ),
          onOk() {
            onFinish(rest);
          },
          onCancel() {
            onFinish();
          },
        });
      } else {
        onFinish();
      }
      async function onFinish(rest = {}) {
        restRef.current = rest;
        if (courseData?.entityData?.related_courses) {
          setRelatedConfirmVis(true);
        } else {
          handleSetRelatedCourse(rest);
        }
      }
    } catch (error) {
      message.error(error.message);
    }
  };
  const relatedCancel = () => {
    setRelatedType(0);
    setRelatedConfirmVis(false);
  };
  const { semesterList, getSemesters } = useSemester();
  const [logOpen, setLogOpen] = useState<boolean>(false);

  const handleLogShow = () => {
    setLogOpen(true);
  };
  useEffect(() => {
    if (
      getPermission(
        ['spoc', 'training', 'mooc'],
        '_course_release_review',
        true,
        queryData.type,
      )
    ) {
      getLog(queryData.id);
    }
  }, [parameterConfig]);
  const [logList, setLogList] = useState<any>([]);
  const logTypeText = [
    '对课程进行了预览。',
    '对课程进行了编辑。',
    '发起了课程发布审核。',
    '驳回了课程。',
    '课程通过审核，已发布。',
  ];
  const getLog = (courseId: string) => {
    reqLogs({ courseId }).then((res: any) => {
      if (res.status === 200) {
        setLogList(
          res.data?.map((item: any) => ({
            title: `${moment(item.approvalTime).format('YYYY-MM-DD HH:mm:ss') ||
              '-'} ${item.processName ?? ''}`,
            subTitle: item.approvalUserName,
            description: (
              <>
                <div className="review-des">
                  {logTypeText[item.operateType]}
                </div>
                {item.operateType === 3 && (
                  <div className="review-des">
                    驳回理由：{item.rejectReason || '无'}
                  </div>
                )}
              </>
            ),
          })) ?? [],
        );
      }
    });
  };
  const mapPublish = (publishType: '1' | '2', courseRelatedDTO: any = null) => {
    const data = [
      {
        courseRelatedDTO,
        id: queryData.id,
        courseSemester: queryData.sm,
        publishType: Number(publishType),
        courseType: typeEn[queryData.type],
      },
    ];
    publishMapNew(data).then((res: any) => {
      if (res.status === 200) {
        const messageText =
          isMoocReview && publishStatus !== 2
            ? `${isSuper
              ? t('提交成功，您是管理员，已为您自动完成课程发布审核')
              : t('课程已提交至管理员审核！')
            }`
            : t('课程发布成功！');
        message.success(messageText);
        setPublishStatus(isMoocReview && !isSuper ? 2 : 1);
        getName();
        getAct();
        setRelatedCoursesModalVisible(false);
        setRelease(false);
      } else {
        message.error(res.message);
      }
    }).finally(() => {
      setPublishLoading(false);
    });
  };
  const dropdownItems = [
    { key: '1', label: '发布为公开课' },
    { key: '2', label: '发布为班级课' },
  ];

  // 图谱map:  敏感词检查
  const checkMapCourseContent = (callback: () => void) => {
    // 仅发布操作：发布前先进行敏感词检测
    const params = {
      coursePublishInfos: [{ courseId: queryData.id }],
      courseType: courseData.entityData.courseType,
    }
    courseOfSensitivew(params).then((res) => {
      if (res?.statusCode === 200 && res.data?.length > 0) {
        Modal.confirm({
          title: t("课程包含敏感内容，是否继续发布？"),
          content: sensitiveMsgListDom(res.data),
          onOk() {
            callback();
          },
          onCancel() { }
        });
      } else {
        callback(); // 不存在敏感词
      }
    })
  }
  const onDropdownSelected = ({ key }: any) => {
    const saveMapCourse = () => {
      if (key === '1') {
        mapPublish('1');
      } else {
        setRelatedCoursesModalVisible(true);
      }
    }
    saveMapCourse();
    // checkMapCourseContent(saveMapCourse); // 敏感词检查
  };

  return !permission ? (
    <div className="no_access">
      <img src="/learn/workbench/img/course/no_access.png" />
      <span>{t('您无权访问该课程')}</span>
    </div>
  ) : (
    <div
      className={`edit-course ${parameterConfig.target_customer ===
        CUSTOMER_NPU && 'base-npu'} ${queryData.type === 'microMajor' &&
        'micro-major-container'}`}
    >
      {parameterConfig.target_customer === CUSTOMER_NPU ? (
        <NPUHeader />
      ) : (
        <Header />
      )}
      <div className="edit-top">
        <div className="top-left-info">
          <a
            onClick={() => {
              if (queryData.type === 'microMajor') {
                history.push('/micromajor/course?micromajor=true');
              } else {
                history.push(
                  `/course${queryData.type === 'mooc'
                    ? '/mooccourse'
                    : ['training', 'map'].includes(queryData.type)
                      ? `/${queryData.type}Course`
                      : ''
                  }`,
                );
              }
            }}
          >
            {parameterConfig.target_customer === CUSTOMER_NPU ? (
              <LeftCircleFilled />
            ) : (
              <>
                <LeftOutlined />
                {t('返回')}
              </>
            )}
          </a>
          <div className="info_box">
            <span>{courseData?.name}</span>
            <span
              className={`publishStatus ${publishStatus === 1
                ? 'published'
                : publishStatus === 2
                  ? ''
                  : courseData?.entityData?.approvalStatus === 1
                    ? 'reject'
                    : 'unpublished'
                }`}
            >
              {publishStatus === 1
                ? t('已发布')
                : publishStatus === 2
                  ? t('待审核')
                  : courseData?.entityData?.approvalStatus === 1
                    ? t('已驳回')
                    : t('未发布')}
            </span>
            {history.location.query.type === 'mooc' && (
              <Select
                value={Number(queryData.sm ?? 1)}
                onSelect={(value: number) => {
                  if (value) {
                    Modal.confirm({
                      content: t('是否切换至该期次？'),
                      onOk() {
                        location.href = changeURLArg(
                          window.location.href,
                          'sm',
                          String(value),
                        );
                        location.reload();
                      },
                    });
                  } else {
                    history.push(
                      `/editcourse/${!['map', 'mooc'].includes(queryData.type)
                        ? 'baseInfo'
                        : 'moocbaseinfo'
                      }?${location.hash.split('?')[1]}`,
                    );

                    sessionStorage.setItem('jumpSemester', '1');
                    return false;
                  }
                }}
              >
                {semesterList.map(semester => (
                  <Select.Option
                    value={semester.courseSemesterId}
                    key={semester.courseSemesterId}
                  >
                    {semester.courseSemesterName}
                  </Select.Option>
                ))}
                <div>{t('期次管理')}</div>
              </Select>
            )}
          </div>

          {/* <div className="buttom_box">
            <div className="course-data">
            <span>发布数：</span>
            <div>
             <span className="publish">{publishCount}</span> /{' '}
             {resourcesCount}
            </div>
            </div>
            </div> */}
        </div>
        <div className="top-right-opt">
          <div className="btn-wrp">
            {(queryData.type === 'spoc' ||
              (queryData.type === 'map' &&
                courseData?.entityData?.publishType === 2)) && (
                <div className="related-course">
                  {courseData?.entityData?.related_courses ? (
                    <span>
                      <span>{t('已绑定课程：')}</span>
                      <span
                        className="course-name"
                        title={`${relatedCourse.course_name}（${relatedCourse.class_teaching}）`}
                      >
                        {relatedCourse.course_name}（
                        {relatedCourse.class_teaching}）
                      </span>
                      <a onClick={() => setRelatedCoursesModalVisible(true)}>
                        {t('更换')}
                      </a>
                    </span>
                  ) : (
                    <div>
                      <span>{t('还未绑定课程')}</span>
                      <Tooltip
                        title={
                          t('绑定课程后可实现一键导入本班学生，并关联上本班的') +
                          t('在线课堂')
                        }
                      >
                        <QuestionCircleOutlined />
                      </Tooltip>
                      <a onClick={() => setRelatedCoursesModalVisible(true)}>
                        {t('立即绑定')}
                      </a>
                    </div>
                  )}
                </div>
              )}
            {getPermission(
              ['training', 'spoc'],
              ['training_course_live_display', 'spoc_course_live_display'],
            ) && (
                <Button
                  type={`${parameterConfig.target_customer === CUSTOMER_NPU
                    ? 'default'
                    : 'primary'
                    }`}
                  style={{ marginRight: '30px' }}
                  ghost
                  onClick={() => setLiveVisible(true)}
                >
                  {showLiveBtn ? t('开启课程') : t('暂不开课')}
                </Button>
              )}
            <Button
              type={`${parameterConfig.target_customer === CUSTOMER_NPU
                ? 'default'
                : 'primary'
                }`}
              ghost={parameterConfig.target_customer === CUSTOMER_NPU}
              onClick={() => {
                if (queryData.type == 'map') {
                  window.open(
                    `#/mapv4?id=${queryData.id}&sm=${queryData.sm}&preview=1`,
                  );
                } else if (queryData.type == 'microMajor') {
                  window.open(
                    `#/microhome?id=${queryData.id}&planId=${queryData.planId}&type=microMajor&sm=${queryData.sm}&preview=1`,
                  );
                } else {
                  window.open(
                    `/learn/course/preview/${queryData.type}/${queryData.id}?preview=1&show=1&type=released`,
                  );
                }
              }}
            >
              {t('预览')}
            </Button>
            {isSuper && publishStatus === 2 && (
              <Button
                type={`${parameterConfig.target_customer === CUSTOMER_NPU
                  ? 'default'
                  : 'primary'
                  }`}
                onClick={() => {
                  setReleaseOrNot(false);
                  setRelease(true);
                  setIsReject(true);
                }}
              >
                {t('驳回')}
              </Button>
            )}

            {publishStatus === 1 ? (
              ((history.location.query.type === 'spoc' && getOptAuth('remove', 2)) || history.location.query.type === 'microMajor') && <Button
                type={`${parameterConfig.target_customer === CUSTOMER_NPU
                  ? 'default'
                  : 'primary'
                  }`}
                // shape="round"
                onClick={() => {
                  setReleaseOrNot(false);
                  setRelease(true);
                }}
              >
                {t(
                  `下架${history.location.query.type === 'microMajor' ? '' : '课程'
                  }`,
                )}
              </Button>
            ) : (
              <Tooltip
                title={
                  !isSuper &&
                  publishStatus == 2 && (
                    <div className="recall-box">
                      {t('已提交审核，')}
                      <a
                        onClick={() => {
                          setReleaseOrNot(false);
                          setRelease(true);
                          setRecall(true);
                        }}
                      >
                        {t('撤回')}
                      </a>
                    </div>
                  )
                }
              >
                {queryData.type === 'map' &&
                  publishStatus !== 2 &&
                  !courseData?.entityData?.publishType ? (
                  <Dropdown
                    menu={{ items: dropdownItems, onClick: onDropdownSelected }}
                  >
                    <Button
                      type={`${parameterConfig.target_customer === CUSTOMER_NPU
                        ? 'default'
                        : 'primary'
                        }`}
                    >
                      {t('发布课程')}
                    </Button>
                  </Dropdown>
                ) : (
                  <Button
                    type={`${parameterConfig.target_customer === CUSTOMER_NPU
                      ? 'default'
                      : 'primary'
                      }`}
                    disabled={!isSuper && publishStatus == 2}
                    // shape="round"
                    onClick={() => {
                      setReleaseOrNot(true);
                      setRelease(true);
                    }}
                  >
                    {t('发布课程')}
                  </Button>
                )}
              </Tooltip>
            )}
            {getPermission(
              ['spoc', 'training', 'mooc'],
              '_course_release_review',
              true,
              queryData.type,
            ) &&
              logList.length > 0 && (
                <Button
                  style={{ marginLeft: '30px' }}
                  type={`${parameterConfig.target_customer === CUSTOMER_NPU
                    ? 'default'
                    : 'primary'
                    }`}
                  onClick={handleLogShow}
                >
                  {t('审核日志')}
                  {/* {(isSuper && publishStatus != 2 || !isSuper) && isMoocReview ? t("完成编辑") : t("发布课程")} */}
                </Button>
              )}
          </div>
        </div>
      </div>
      <div className="edit-detail">
        <div className="edit-menu">
          <div className="course-img">
            <img src={basicCover}></img>
          </div>
          {courseData?.entityData?.certification_type &&
            courseData?.entityData?.inside_isbn &&
            ((courseData.entityData.courseType == 1 &&
              parameterConfig?.mooc_certification_display == 'true') ||
              (courseData.entityData.courseType == 2 &&
                parameterConfig?.spoc_certification_display == 'true')) && (
              <div className="certification">
                <div className="type">
                  <div className="circle">
                    <IconFont
                      width="18px"
                      height="18px"
                      type="icontongyirenzheng_1"
                    />
                  </div>
                  <span
                    className="name"
                    title={courseData?.entityData?.certification_type}
                  >
                    {courseData?.entityData?.certification_type}
                  </span>
                </div>
                <div className="isbn">
                  <span>
                    {t('认证号:')}
                    {courseData?.entityData?.inside_isbn}
                  </span>
                </div>
              </div>
            )}
          <div className="menu-container">
            <Menu
              style={{ width: 230 }}
              className="edit-menulist"
              // defaultSelectedKeys={['1']}
              selectedKeys={[activeMenuKey]}
              // onSelect={(info: any) => {
              //   setActiveMenuKey(info.selectedKeys[0]);
              // }}
              mode="inline"
            >
              {getMenu()}
            </Menu>
            <div className="nav-setting-wrp">
              <Button
                type="link"
                icon={<UnorderedListOutlined />}
                onClick={() => {
                  setStageMenus(showMenus);
                  setShowMenuSetting(true);
                }}
              >
                {t('导航管理')}
              </Button>
            </div>
          </div>
          {showMenuSetting && (
            <div className="menu-setting-container">
              <div className="menu-setting-wrp">
                {mList
                  .filter((item: any) => item.show)
                  .map((item: any) => (
                    <div className="menu-setting-item" key={item.key}>
                      <div
                        style={{
                          color: stageMenus.includes(item.key)
                            ? 'rgba(0, 0, 0, 0.85)'
                            : 'rgba(0, 0, 0, 0.25)',
                          display: "flex"
                        }}
                      >
                        {item.icon}
                        {item.name}
                      </div>
                      {![
                        'baseInfo',
                        'moocbaseinfo',
                        'studentmanagement',
                        'studentlist',
                        'statistics',
                        'moocstatistics',
                        'mapstatistics',
                        'operation',
                        'moocoperation',
                        'mooccompletion',
                      ].includes(item.key) && (
                          <Switch
                            size="small"
                            defaultChecked={showMenus.includes(item.key)}
                            onChange={(checked: boolean) => {
                              if (checked) {
                                setStageMenus(stageMenus.concat([item.key]));
                              } else {
                                setStageMenus(
                                  stageMenus.filter(
                                    (item_: any) => item_ !== item.key,
                                  ),
                                );
                              }
                            }}
                          />
                        )}
                    </div>
                  ))}
              </div>
              <div className="menu-setting-bottom">
                <div className="menu-tips">
                  {t('关闭的导航在学生视角也会关闭')}
                </div>
                <div className="menu-btn-wrp">
                  <Button
                    type="primary"
                    ghost
                    onClick={() => {
                      setStageMenus(showMenus);
                      setShowMenuSetting(false);
                    }}
                  >
                    {t('取消')}
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      baseInfo
                        .updateMenuSetting({
                          courseId: queryData.id,
                          menu_show_setting: JSON.stringify(stageMenus),
                        })
                        .then((res: any) => {
                          if (res.status === 200) {
                            setShowMenus(stageMenus);
                            setShowMenuSetting(false);
                          } else {
                            message.error(res.message || t('保存失败'));
                          }
                        });
                    }}
                  >
                    {t('确定')}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="edit-content">{children}</div>
      </div>
      <Modal
        title={
          releaseOrNot ? t(`发布${sensitiveWordInfos?.length > 0 ? '（课程包含敏感内容）' : ''}`) : `${recall ? t('撤回') : isReject ? t('驳回') : t('下架')}`
        }
        visible={release}
        confirmLoading={publishLoading}
        onOk={handleReleseOK}
        onCancel={() => setRelease(false)}
      >
        {isReject ? (
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Input.TextArea
                rows={4}
                style={{ width: '100%' }}
                placeholder={t('请输入驳回原因')}
                onChange={(e: any) => {
                  setRejectReason(e.target.value);
                }}
              />
            </div>
          </>
        ) : (
          <Spin spinning={releaseLoading}>
            {releaseOrNot
              ?
              <div>
                <div>
                  {
                    ((isSuper && publishStatus != 2) || !isSuper) && isMoocReview
                      ? t('发布后将提交至管理员进行审核发布，是否确认提交？')
                      : t('确定要发布该课程？')
                  }
                </div>
                {sensitiveMsgListDom(sensitiveWordInfos)}
              </div>

              : `${recall
                ? t('确定要撤回课程发布审核？')
                : isReject
                  ? t('确定要驳回该课程？')
                  : t('确定要下架该课程？')
              }`}
          </Spin>
        )}
      </Modal>
      <Modal
        title={t('二次确定')}
        visible={liveVisible}
        onOk={handleConfirm}
        onCancel={() => setLiveVisible(false)}
      >
        {t('确定要')}
        {showLiveBtn ? t('开启') : t('暂时关闭')}
        {t('该课程吗？')}
        {!showLiveBtn && (
          <Form
            className="liveCloseForm"
            form={form}
            onFinish={handleConfirmClose}
          >
            <Form.Item
              label={t('关闭原因')}
              name="staus"
              rules={[{ required: true, message: t('请选择关闭原因') }]}
            >
              <Select>
                <Select.Option value={1}>
                  {t('本课程不适合开启线上课程')}
                </Select.Option>
                <Select.Option value={2}>{t('其他')}</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label={t('备注')} name="reason">
              <Input.TextArea placeholder={t('请输入备注')} rows={4} />
            </Form.Item>
          </Form>
        )}
      </Modal>
      <Modal
        title={t('确认操作')}
        wrapClassName="relate-confirm-modal"
        open={relatedConfirmVis}
        onCancel={relatedCancel}
        onOk={() => handleSetRelatedCourse(restRef.current)}
        confirmLoading={relatedLoading}
      >
        <div>{t('请确认更换绑定的课程后：')}</div>
        <Radio.Group
          value={relatedType}
          onChange={(e: any) => setRelatedType(e.target.value)}
        >
          <Radio value={0}>{t('不修改班级里的学生')}</Radio>
          <Radio value={1}>{t('按新绑定的课程重新导入学生')}</Radio>
        </Radio.Group>
        <div className="tips">
          {t(
            '*重新导入后当前班级内学生将会被删除，且学情数据将会清空，请谨慎操作',
          )}
        </div>
      </Modal>
      <Modal
        title={t('绑定课程')}
        visible={relatedCoursesModalVisible}
        footer={
          <Space>
            <Button onClick={() => setRelatedCoursesModalVisible(false)}>
              取消
            </Button>
            {queryData.type === 'map' && (
              <Button onClick={() => mapPublish('2')}>不绑定，直接发布</Button>
            )}
            <Button type="primary" onClick={handleRelatedConfirm}>
              确定
            </Button>
          </Space>
        }
        onCancel={() => setRelatedCoursesModalVisible(false)}
      >
        <Form
          form={relatedForm}
          initialValues={{}}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            style={{ marginBottom: '8px' }}
            name="course"
            label={t('选择课程')}
            rules={[{ required: true, message: t('请选择课程') }]}
          >
            <Select
              style={{ width: '100%' }}
              showSearch
              onSearch={handleSearch}
              onPopupScroll={handleScroll}
              filterOption={false}
              // onChange={handleChange}
              // key={JSON.stringify(offlineCourse)}
              allowClear={true}
            >
              {offlineCourse.map((course: any) => (
                <Select.Option key={course.id} value={course.id}>
                  {`${course.course_name} ${course.class_teaching ? `（${course.class_teaching}）` : ''
                    }`}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {/* <div style={{ color: "#aaa", marginLeft: "117px" }}>*课程绑定后将无法修改</div> */}
        </Form>
      </Modal>
      <LoggerModal
        list={logList}
        open={logOpen}
        onClose={() => setLogOpen(false)}
      />
      <Loading />
    </div>
  );
};

export default EditCourse;
