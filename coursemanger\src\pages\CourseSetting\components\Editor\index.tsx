import { uploadFile } from '@/api/homework';
import { Modal, Spin, message } from 'antd';
import React, { FC, memo, useEffect, useState } from 'react';
import { useLocation, useSelector } from 'umi';
import './index.less';
// @ts-ignore
import { commonUpload, storageConfig, uploadImport } from '@/api/addCourse';
import useLocale from '@/hooks/useLocale';
import aliyun from '@/otherStorage/aliyun';
import OOSDK from '@/otherStorage/ctyun';
import { getSensitiveWord } from '@/utils';
import { LoadingOutlined } from '@ant-design/icons';
import $ from 'jquery';

interface IEditor {
  name: string;
  value?: any;
  height?: any;
  disabled?: boolean;
  onlyText?: boolean;
  onChange?: (e: any) => void;
  addBlanks?: () => void;
  wordlimit?: number;
}
const DEFAULT_TYPES = 'jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp';
const IMAGE_TYPES = `${DEFAULT_TYPES},${DEFAULT_TYPES.toUpperCase()}`;

const Editor: FC<IEditor> = ({
  name,
  value,
  height,
  disabled = false,
  onChange,
  addBlanks,
  wordlimit = false,
  onlyText = false,
}) => {
  // const [initInstance, setInitInstance] = useState<boolean>(false); // 编辑器是否初始化完毕
  // const [isFirst, setIsFirst] = useState<boolean>(true);
  const dragIds = useSelector<Models.Store, any>(
    state => state.moocCourse.dragIds,
  );
  const { t } = useLocale();
  const location: any = useLocation();

  // useEffect(() => {
  //   if (value != null && isFirst) {
  //     if (name && value !== '<p><br data-mce-bogus="1"></p>') {
  //       // 手动规避editor获取焦点必会触发的bug
  //       // (window as any).tinymce.get(String(name))?.setContent(value);
  //     }
  //     setIsFirst(false);
  //   }
  // }, [value]);
  useEffect(() => {
    if (dragIds.includes(name)) {
      (window as any).tinymce.execCommand(
        'mceRemoveEditor',
        true,
        String(name),
      );
      (window as any).tinymce.execCommand('mceAddEditor', true, String(name));
    }
  }, [dragIds]);
  useEffect(() => {
    (window as any).tinymce.editors[name]?.remove();
    initEditor();
  }, [disabled]);

  // `创建自定义accept属性的input框`
  function createInput(accept: any) {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', accept);
    return input;
  }

  const completeUpload = (params: any) => {
    uploadImport(
      {
        oosPath: `http://${params.bucket}.${params.endpoint}/${params.key}`,
        fileLength: params.file.size as number,
        courseName: params?.file?.name,
        courseId: location.query.id,
      },
      (e: any) => { },
    ).then(dd => {
      if (dd && dd.data && dd.success) {
        params.callback(dd.data?.httpPath);
        message.success(`${params.file.name}${t('上传成功')}`);
      } else {
        message.error(dd.error?.title || t('上传失败'));
      }
    });
  };

  const otherPlatform = async (params: any) => {
    if (params.product === 'ctyun') {
      const res: any = await OOSDK.putObject({
        Bucket: params.bucket,
        Body: params.file.originFileObj,
        Key: params.key,
      });

      if (res?.ETag) {
        completeUpload(params);
      }
    } else if (params.product === 'aliyun') {
      aliyun.init(params);
      const result = await aliyun.putObject(params);
      if (result.url) {
        completeUpload(params);
      }
    } else {
    }
  };

  const [isLoading, setIsLoading] = useState(false);
  const [modalContent, setModalContent] = useState('上传中...');
  // 初始化富文本编辑器
  const initEditor = () => {
    (window as any).tinymce
      .init({
        selector: `#${name}`,
        // toolbar: 'undo redo | styleselect | bold italic | link image strikethrough tiny_mce_wiris_formulaEditor'
        toolbar: disabled
          ? []
          : onlyText
            ? [
              'undo redo | forecolor backcolor bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
              'formatselect fontselect fontsizeselect',
            ]
            : [
              'undo redo | forecolor backcolor bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist | emoticons charmap',
              'formatselect fontselect fontsizeselect table image media link',
              // "formatselect fontselect fontsizeselect table image"
            ],
        // plugins:['table'],
        menubar: '',
        branding: false,
        height: height,
        width: '100%',

        // automatic_uploads: false, //图像是使用imagetools插件处理后插入到内容区的，此时图像并未真正上传到服务器，而是以Data URL/Blob URL的方式插入在内容中
        language: 'zh_CN',
        // external_plugins: { tiny_mce_wiris: 'https://www.wiris.net/demo/plugins/tiny_mce/plugin.js' }, //注册公式插件 media
        // external_plugins: { 'tiny_mce_wiris': `/wiris/mathtype-tinymce5/plugin.min.js` },
        plugins: disabled
          ? ''
          : onlyText
          ? 'kityformula-editor  ax_wordlimit'
          : `image media  kityformula-editor link emoticons charmap table lists  ax_wordlimit`,
        // images_upload_base_path: '/demo',
        images_upload_handler: images_upload_handler, //图片上传
        file_picker_callback: function (callback: any, value: any, meta: any) {
          const curFileUpload = async function (fileType: string) {
            const curFile = (this as any)?.files?.[0];
            if (curFile?.size / 1024 / 1024 > 100) {
              //100M以上的文件请先上传至资源库
              Modal.error({
                content: '10M以上的文件请先上传至资源库',
                zIndex: 2001,
              });
              return;
            }

            if (curFile?.name) {
              const canUpload: any = await getSensitiveWord(
                curFile.name,
                '文件名',
                () => true,
                () => false,
              );
              if (!canUpload) {
                Modal.error({
                  content: '文件包含敏感词，请修改后再操作',
                  zIndex: 2001,
                });
                return false;
              }
            }
            const ress: any = await storageConfig([
              {
                fileName: curFile.name,
                fileLength: curFile.size,
                fileType: curFile.type,
              },
            ]);
            if (!ress?.success) {
              Modal.error({
                content: t('存储初始化失败，请重试'),
                zIndex: 2001,
              });
              return;
            }

            const storage_: any = ress.data[0];
            if (storage_.access_type === 'OSS') {
              otherPlatform({ ...storage_, file: curFile, callback });
            } else if (storage_.access_type === 'NAS') {
              const formData_ = new FormData();
              setIsLoading(true);
              formData_.append('file', curFile, curFile.name);
              const res: any = await commonUpload(
                formData_,
                {},
                (e: any) => { },
              );
              if (res.success) {
                if (fileType === 'media') {
                  callback(res?.data?.httpPath);
                }
                if (fileType === 'file') {
                  callback(res.data.httpPath, { text: curFile.name });
                }
              } else {
                Modal.error({ content: '上传失败', zIndex: 2001 });
              }
              setIsLoading(false);
            }
          };
          if (meta.filetype === 'media') {
            const input = createInput(
              '.mp3, .mp4, .avi, .mkv, .wmv、.mov、.flv',
            );
            input.click();
            input.onchange = function () {
              return curFileUpload.call(this, 'media');
            };
          }
          if (meta.filetype == 'file') {
            const input = createInput(
              '.pdf, .txt, .doc, .docx, .xls, .xlsx, .ppt, .pptx',
            );
            input.click();
            input.onchange = function () {
              return curFileUpload.call(this, 'file');
            };
          }
        },
        images_file_types: IMAGE_TYPES,
        relative_urls: false,
        convert_urls: false,
        image_caption: true,
        powerpaste_word_import: 'propmt', // 参数可以是propmt, merge, clear，效果自行切换对比
        powerpaste_html_import: 'propmt', // propmt, merge, clear
        powerpaste_allow_local_images: true,
        powerpaste_keep_unsupported_src: true,
        powerpaste_block_drop: false,
        paste_data_images:false,
        // ax_wordlimit_num: wordlimit, //限制字数
        // ax_wordlimit_delay:1000,  //限制字数方法回调延迟时间
        paste_as_text: false,
        // ax_wordlimit_callback: function(editor: any, txt: any, num: any) {
        //   var cnt = editor.getContent({ format: 'html' });
        //   console.log(cnt,cnt.toString().length,cnt.length,'cnt',num)
        //   if (cnt.length > num) {
        //     editor.execCommand('undo');
        //     // 光标移动到文本的最后
        //     editor.execCommand('selectAll');
        //     editor.selection.getRng().collapse(false);
        //     editor.focus();
        //     message.info(t('字数超过{name}了，限制{name}字以内', String(num)));
        //   }
        // },
        imagetools_cors_hosts: ['img-blog.csdnimg.cn', 'statics.xiumi.us'], //这个是第三方插件 需要配置 plugins 里面的 imagetools  然后加上图片的域名 防止跨域
        setup: (editor: any) => {
          editor.on('change', (e: any) => {
            let temp = e.level.content;
            if (temp === '<p><br data-mce-bogus="1"></p>') {
              temp = '';
            }
            if (onlyText) {
              onChange &&
                onChange({ ...e, target: { value: temp } });
            } else {
              onChange &&
                onChange({ ...e, level: { content: temp } });
            }
            // setIsFirst(false);
          });
          editor.on('keyup change paste undo redo', () => {
            // 获取包含 HTML/CSS 的完整内容
            const fullHtml = editor.getContent({ format: 'html' });
            // 计算总长度（包含所有标签和样式）
            const totalLength = fullHtml.length;
            // 更新统计展示（假设有个 id=wordCount 的元素）
            console.log(fullHtml,totalLength,'totalLength')
              if (totalLength > wordlimit) {
                editor.execCommand('undo');
                // 光标移动到文本的最后
                editor.execCommand('selectAll');
                editor.selection.getRng().collapse(false);
                editor.focus();
                message.info(t('字数超过{name}了，限制{name}字以内', String(wordlimit)));
              }
          });
          // editor..setAttribute("contenteditable", false);
          // editor.ui.registry.addButton('addblanks', {
          //   text: '<i class="addBlanks_btn">[填空]</i>',
          //   tooltip: '插入填空',
          //   // icon:'accessibility-check',
          //   onAction: function () {
          //     // editor.insertContent(`<span class="blank_">_______</span>`);
          //     addBlanks && addBlanks();
          //   }

          // });
        },
        init_instance_callback: function (editor: any) {
          // setInitInstance(true);
          $(editor.getContainer())
            .find('button.tox-statusbar__wordcount')
            .click();
          if (disabled) {
            editor.iframeElement.contentDocument.body.setAttribute(
              'contenteditable',
              false,
            );
          }
          // console.log('editor', editor.iframeElement.);
          if (value) {
            (window as any).tinymce.editors[name].setContent(value)
          }
        },
      })
      .then(() => { });
  };
  //插入内容 insertContent
  // 单图上传图片
  const images_upload_handler = (blobInfo: any, succFun: any, failFun: any) => {
    const formData = new FormData();
    formData.append('file', blobInfo.blob(), blobInfo.filename());
    uploadFile(formData).then((res: any) => {
      if (res.success) {
        succFun(res.data);
      } else {
        failFun(res.error?.title || t('上传失败'));
      }
    });
  };
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  return (
    <div className={`tox-container ${disabled ? 'tox-disabled' : ''}`}>
      <div id={name}></div>
      <Modal
        title=""
        open={isLoading}
        closeIcon={false}
        zIndex={2000}
        footer={null}
        closable={false}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Spin indicator={antIcon} size="large" />
          文件上传中...
        </div>
      </Modal>
    </div>
  );
};

export default memo(Editor);
