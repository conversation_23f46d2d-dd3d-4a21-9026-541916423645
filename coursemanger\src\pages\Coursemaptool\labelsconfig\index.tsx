import React,{ useEffect } from 'react';
import './index.less';
import { Tabs } from 'antd';
import LabelTable from './components/LabelTable';
import { useSelector } from 'umi';

const  labelsconfig = () => {
    const { userInfo } = useSelector<any, any>((state) => state.global);
    
    const isSuper = userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') || //是否是系统管理员
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager') || //是否是课程管理员
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_second_manager') || //第二权限
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin

    return <div className='labelsconfig_view'>
        <Tabs destroyInactiveTabPane>
            <Tabs.TabPane tab="公共标签" key="item-1">
                <LabelTable type={0} />
            </Tabs.TabPane>
            {isSuper && <Tabs.TabPane tab="资源标签" key="item-2">
                <LabelTable type={2} />
            </Tabs.TabPane>}
            <Tabs.TabPane tab="个人标签" key="item-3">
                <LabelTable type={1} />
            </Tabs.TabPane>
        </Tabs>
    </div>;
};

export default labelsconfig;