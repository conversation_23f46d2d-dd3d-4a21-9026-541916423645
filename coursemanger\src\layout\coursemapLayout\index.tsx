import React, { useState, useEffect, FC, ReactNode } from 'react';
import {
  getUserJurisdictionV2,
} from '@/api/course';
import './index.less';
import '@/assets/styles/index.css';

import { Layout, message, Menu, Tabs } from 'antd';
import { Link, useHistory } from 'react-router-dom';

import { useDispatch, useLocation } from 'umi';
import { HashRouter } from 'react-router-dom';
import courseTemplate from '@/api/courseTemplate';
import Loading from '@/components/loading/loading';
import { useSelector } from '@@/plugin-dva/exports';
import ModuleCfg, { CUSTOMER_NPU, CUSTOMER_CXD } from '@/permission/moduleCfg';
import { ModuleCfg2 } from '@/permission/moduleCfg';
import perCfg from '@/permission/config';
import Header from '@/components/Header';
import LeftMenu from '@/components/LeftMenu';
import NPUHeader from '@/components/NPUHeader';
import useLocale from '@/hooks/useLocale';
import useCkeckRoles from '@/hooks/useCkeckRoles';
const { Content, Sider } = Layout;

interface ICatalogue {
  contentId: string;
  description: string;
  name: string;
  parentPath: string;
  type: string;
  path: string;
}

const App: FC<{ children?: ReactNode }> = ({ children }) => {
  const { t } = useLocale();
  let history: any = useHistory();
  // 获取url参数
  const { query, pathname }: any = useLocation();
  const [selectedKey, setSelectedKey] = useState<string>('/coursemap/minemap');
  // const [catalogueList, setCatalogueList] = useState<ICatalogue[]>([]);
  const [hash, setHash] = useState(window.location.hash);
  const dispatch = useDispatch();
  const permissionModules = useSelector<any, any>(
    ({ jurisdiction }) => jurisdiction.modules,
  );

  const { parameterConfig, buttonPermission, permission, userInfo } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any, userInfo: any }
  >(state => state.global);
  let path = useLocation().pathname;
  const active = useLocation().pathname
    ? useLocation().pathname.split('/')[1]
    : '';
  useEffect(() => {
    getJurisdiction();
    screenResize();
    window.addEventListener('resize', function () {
      //移动端判定
      screenResize();
      return () => {
        window.removeEventListener('resize', screenResize);
      };
    });
  }, []);
  const getHash = () => {
    setHash(window.location.hash);
  };

  useEffect(() => {
    if(userInfo?.roles){
      let roles = userInfo.roles?.map((item:any)=>item.roleCode) || [];
      useCkeckRoles(roles);
    }
  }, [userInfo?.roles])

  //移动端适配
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any; }, any>(
    ({ config }) => config);

  const screenResize = () => {
    //移动端判定
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      window.innerWidth < 768
    ) {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: true,
          menuShowChange: false,
        },
      });
    } else {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: false,
          menuShowChange: true,
        },
      });
    }
  };
  window.onhashchange = () => {
    getHash();
  };
  const tabChange = (e: any) => {
    let newurl = '';
    if (e == 'marjor') {
      newurl = `/coursemap/majormap`;
    } else if (e == 'mine') {
      newurl = `/coursemap/minemap`;
    } else {
      newurl = `/coursemap/sharetemap`;
    }
    if (query.micromajor) {
      newurl = newurl + `?micromajor=${query.micromajor}`;
    }
    history.push(newurl);
  };
  const getJurisdiction = () => {
    getUserJurisdictionV2()
      .then(res => {
        if (
          res &&
          res.errorCode === 'success' &&
          typeof res.extendMessage.moduleFeatures === 'object'
        ) {
          const permission = res.extendMessage.moduleFeatures;
          dispatch({
            type: 'jurisdiction/updateState',
            payload: {
              jurisdictionList: Object.keys(permission)
                .map(key => permission[key])
                .flat(2),
              modules: res.extendMessage.modules || [],
            },
          });
        } else {
          message.error(t('权限信息获取失败！'));
        }
      })
      .catch(error => {
        message.error(t('权限信息获取失败！'));
      });
  };

  return (
    <div className={`App ${mobileFlag ? ' mobileContainer' : ''}`}>
    <Layout>
      {parameterConfig.target_customer === CUSTOMER_NPU ? (
        <NPUHeader />
      ) : (
        <Header />
      )}
      <div className={`content-box`} style={{ height: '100%' }}>
        <Layout>
          {parameterConfig.target_customer != null &&
            parameterConfig.target_customer !== CUSTOMER_NPU && parameterConfig.target_customer !== CUSTOMER_CXD  && (
              <LeftMenu />
            )}
          <Sider
            width={180}
            theme="light"
            className="site-layout-background none"
          >
            <Menu
              defaultSelectedKeys={[path]}
              mode="inline"
              theme="light"
              defaultOpenKeys={['minemap']}
            >
              <Menu.Item key={'/coursemap/majormap'}>
                <Link
                  to={`/coursemap/majormap${
                    query.micromajor ? '?micromajor=true' : ''
                  }`}
                >
                  {t('专业地图')}
                </Link>
              </Menu.Item>
              <Menu.SubMenu title={t('课程地图')} key="minemap">
                <Menu.Item key={'/coursemap/minemap'}>
                  <Link
                    to={`/coursemap/minemap${
                      query.micromajor ? '?micromajor=true' : ''
                    }`}
                  >
                    {t('我的地图')}
                  </Link>
                </Menu.Item>
                {parameterConfig?.show_publish_map !== 'false' && <Menu.Item key={'/coursemap/sharetemap'}>
                  <Link
                    to={`/coursemap/sharetemap${
                      query.micromajor ? '?micromajor=true' : ''
                    }`}
                  >
                    {t('已发布地图')}
                  </Link>
                </Menu.Item>}
              </Menu.SubMenu>
              <Menu.Item key={'/coursemap/recyclebin'}>
                <Link
                  to={`/coursemap/recyclebin${
                    query.micromajor ? '?micromajor=true' : ''
                  }`}
                >
                  {t('回收站')}
                </Link>
              </Menu.Item>
              <Menu.Item key={'/coursemap/labelsconfig'}>
                <Link
                  to={`/coursemap/labelsconfig${
                    query.micromajor ? '?micromajor=true' : ''
                  }`}
                >
                  {t('自定义标签')}
                </Link>
              </Menu.Item>
            </Menu>
          </Sider>
          <Layout
            // style={{ padding: '15px 15px 0px 15px', background: '#F7F9FA' }}
            className={mobileFlag ? 'mobile_right' : ''}
          >
            {mobileFlag ? (
              <div className="mobile_container">
                <div className="tabs">
                  <Tabs defaultActiveKey="mine" onChange={tabChange}>
                    <Tabs.TabPane
                      key={'marjor'}
                      tab={t('专业地图')}
                    ></Tabs.TabPane>
                    <Tabs.TabPane
                      key={'mine'}
                      tab={t('我的地图')}
                    ></Tabs.TabPane>
                    <Tabs.TabPane
                      key={'share'}
                      tab={t('已发布地图')}
                    ></Tabs.TabPane>
                    <Tabs.TabPane
                      key={'recyclebin'}
                      tab={t('回收站')}
                    ></Tabs.TabPane>
                  </Tabs>
                </div>
                <Content
                  className="site-layout-background"
                  style={{
                    margin: '0 0 0 1px',
                    minHeight: 280,
                    overflowX: 'hidden',
                    overflowY: 'auto',
                  }}
                >
                  {children}
                </Content>
              </div>
            ) : (
              <Content
                className="site-layout-background"
                style={{
                  margin: '0 0 0 1px',
                  minHeight: 280,
                  overflowX: 'hidden',
                  overflowY: 'auto',
                }}
              >
                {children}
              </Content>
            )}

              <Loading />
            </Layout>
          </Layout>
        </div>
      </Layout>
    </div>
  );
};

export default App;
